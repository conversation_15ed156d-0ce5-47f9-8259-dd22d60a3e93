{"version": 3, "file": "sha3-addons.d.ts", "sourceRoot": "", "sources": ["src/sha3-addons.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AACH,OAAO,EAAE,MAAM,EAAE,KAAK,SAAS,EAAE,MAAM,WAAW,CAAC;AACnD,OAAO,EAGL,KAAK,MAAM,EACX,KAAK,OAAO,EAGZ,IAAI,EACJ,KAAK,OAAO,EACZ,KAAK,KAAK,EAGX,MAAM,YAAY,CAAC;AAoCpB,MAAM,MAAM,UAAU,GAAG,SAAS,GAAG;IAAE,eAAe,CAAC,EAAE,KAAK,CAAC;IAAC,MAAM,CAAC,EAAE,KAAK,CAAA;CAAE,CAAC;AA0BjF,MAAM,MAAM,OAAO,GAAG;IACpB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,UAAU,CAAC;IAC5C,SAAS,EAAE,MAAM,CAAC;IAClB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,IAAI,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CAC3C,CAAC;AACF,MAAM,MAAM,UAAU,GAAG;IACvB,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,UAAU,CAAC;IACnD,MAAM,CAAC,IAAI,CAAC,EAAE,UAAU,GAAG,SAAS,CAAC;CACtC,CAAC;AACF,MAAM,MAAM,QAAQ,GAAG;IACrB,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;IAClD,MAAM,CAAC,IAAI,CAAC,EAAE,YAAY,GAAG,YAAY,CAAC;CAC3C,CAAC;AACF,eAAO,MAAM,SAAS,EAAE,OAAiE,CAAC;AAC1F,eAAO,MAAM,SAAS,EAAE,OAAiE,CAAC;AAE1F,qBAAa,IAAK,SAAQ,MAAO,YAAW,OAAO,CAAC,IAAI,CAAC;gBAErD,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,SAAS,EAAE,OAAO,EAClB,GAAG,EAAE,KAAK,EACV,IAAI,GAAE,UAAe;IAavB,SAAS,CAAC,MAAM,IAAI,IAAI;IAIxB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,IAAI;IAW3B,KAAK,IAAI,IAAI;CAGd;AAUD,eAAO,MAAM,OAAO,EAAE;IACpB,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,UAAU,CAAC;IAC5D,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC;CACK,CAAC;AACpD,eAAO,MAAM,OAAO,EAAE;IACpB,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,UAAU,CAAC;IAC5D,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC;CACK,CAAC;AACpD,eAAO,MAAM,UAAU,EAAE;IACvB,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,UAAU,CAAC;IAC5D,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC;CACW,CAAC;AAC1D,eAAO,MAAM,UAAU,EAAE;IACvB,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,UAAU,CAAC;IAC5D,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC;CACW,CAAC;AAI1D,qBAAa,SAAU,SAAQ,MAAO,YAAW,OAAO,CAAC,SAAS,CAAC;gBACrD,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,GAAE,UAAe;IAY1F,SAAS,CAAC,MAAM,IAAI,IAAI;IAKxB,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS,GAAG,SAAS;IAIrC,KAAK,IAAI,SAAS;CAGnB;AAaD,yBAAyB;AACzB,eAAO,MAAM,YAAY,EAAE,UAA6D,CAAC;AACzF,yBAAyB;AACzB,eAAO,MAAM,YAAY,EAAE,UAA6D,CAAC;AACzF,6BAA6B;AAC7B,eAAO,MAAM,eAAe,EAAE,UAAmE,CAAC;AAClG,6BAA6B;AAC7B,eAAO,MAAM,eAAe,EAAE,UAAmE,CAAC;AAGlG,KAAK,YAAY,GAAG,UAAU,GAAG;IAAE,QAAQ,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAEvD,qBAAa,YAAa,SAAQ,MAAO,YAAW,OAAO,CAAC,YAAY,CAAC;IACvE,OAAO,CAAC,QAAQ,CAAC,CAAe;IAChC,SAAS,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,CAAC;IACvC,OAAO,CAAC,QAAQ,CAAK;IACrB,OAAO,CAAC,UAAU,CAAK;IACvB,OAAO,CAAC,QAAQ,CAAS;gBAEvB,QAAQ,EAAE,MAAM,EAChB,SAAS,EAAE,MAAM,EACjB,QAAQ,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,EAC5B,SAAS,EAAE,OAAO,EAClB,IAAI,GAAE,YAAiB;IAgCzB,SAAS,CAAC,MAAM,IAAI,IAAI;IAUxB,UAAU,CAAC,EAAE,CAAC,EAAE,YAAY,GAAG,YAAY;IAQ3C,OAAO,IAAI,IAAI;IAIf,KAAK,IAAI,YAAY;CAGtB;AAqBD,uDAAuD;AACvD,eAAO,MAAM,eAAe,EAAE,QAAoE,CAAC;AACnG,uDAAuD;AACvD,eAAO,MAAM,eAAe,EAAE,QAAoE,CAAC;AACnG,2DAA2D;AAC3D,eAAO,MAAM,kBAAkB,EAAE,QACS,CAAC;AAC3C,uDAAuD;AACvD,eAAO,MAAM,kBAAkB,EAAE,QACS,CAAC;AAG3C,MAAM,MAAM,cAAc,GAAG,SAAS,GAAG;IACvC,CAAC,CAAC,EAAE,MAAM,CAAC;CACZ,CAAC;AAWF,mDAAmD;AACnD,eAAO,MAAM,aAAa,EAAE,OAAqD,CAAC;AAClF,mDAAmD;AACnD,eAAO,MAAM,aAAa,EAAE,OAAqD,CAAC;AAYlF,MAAM,MAAM,YAAY,GAAG;IAAE,KAAK,CAAC,EAAE,MAAM,CAAC;IAAC,eAAe,CAAC,EAAE,KAAK,CAAA;CAAE,CAAC;AAGvE,qBAAa,cAAe,SAAQ,MAAO,YAAW,OAAO,CAAC,cAAc,CAAC;IAC3E,QAAQ,CAAC,QAAQ,QAAQ;IACzB,OAAO,CAAC,QAAQ,CAAC,CAAS;IAC1B,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC;IAC1B,OAAO,CAAC,eAAe,CAAa;IACpC,OAAO,CAAC,QAAQ,CAAK;IACrB,OAAO,CAAC,UAAU,CAAK;gBAErB,QAAQ,EAAE,MAAM,EAChB,OAAO,EAAE,MAAM,EACf,SAAS,EAAE,MAAM,EACjB,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,YAAY;IAMpB,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;IAwBzB,SAAS,CAAC,MAAM,IAAI,IAAI;IAYxB,OAAO,IAAI,IAAI;IAMf,UAAU,CAAC,EAAE,CAAC,EAAE,cAAc,GAAG,cAAc;IAW/C,KAAK,IAAI,cAAc;CAGxB;AACD,+CAA+C;AAC/C,eAAO,MAAM,GAAG,EAAE,MAGZ,CAAC;AACP,oDAAoD;AACpD,eAAO,MAAM,GAAG,EAAE,MAGZ,CAAC;AAEP;;GAEG;AACH,qBAAa,SAAU,SAAQ,MAAM;IACnC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC;gBACX,QAAQ,EAAE,MAAM;IAU5B,MAAM,IAAI,IAAI;IAQd,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;IAKzB,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;IAGvB,SAAS,CAAC,MAAM,IAAI,IAAI;IACxB,UAAU,CAAC,IAAI,EAAE,UAAU,GAAG,UAAU;IAGxC,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU;IAIhC,MAAM,IAAI,IAAI;IAQd,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS,GAAG,SAAS;IAOrC,KAAK,IAAI,SAAS;CAGnB;AAED,gGAAgG;AAChG,eAAO,MAAM,SAAS,GAAI,iBAAc,KAAG,SAAoC,CAAC"}
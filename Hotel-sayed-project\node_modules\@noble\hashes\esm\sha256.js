/**
 * SHA2-256 a.k.a. sha256. In JS, it is the fastest hash, even faster than Blake3.
 *
 * To break sha256 using birthday attack, attackers need to try 2^128 hashes.
 * BTC network is doing 2^70 hashes/sec (2^95 hashes/year) as per 2025.
 *
 * Check out [FIPS 180-4](https://nvlpubs.nist.gov/nistpubs/FIPS/NIST.FIPS.180-4.pdf).
 * @module
 * @deprecated
 */
import { SHA224 as SHA224n, sha224 as sha224n, SHA256 as SHA256n, sha256 as sha256n, } from "./sha2.js";
/** @deprecated Use import from `noble/hashes/sha2` module */
export const SHA256 = SHA256n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const sha256 = sha256n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const SHA224 = SHA224n;
/** @deprecated Use import from `noble/hashes/sha2` module */
export const sha224 = sha224n;
//# sourceMappingURL=sha256.js.map
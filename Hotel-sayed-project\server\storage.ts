import {
  users, hotels, platformTokens, reviews, replies,
  googleBusinessLocations, tripAdvisorLocations, bookingProperties, airbnbListings, syncStatus,
  User, InsertUser,
  Review, InsertReview,
  Reply, InsertReply,
  Hotel, InsertHotel,
  PlatformToken, InsertPlatformToken,
  GoogleBusinessLocation, InsertGoogleBusinessLocation,
  TripAdvisorLocation, InsertTripAdvisorLocation,
  BookingProperty, InsertBookingProperty,
  AirbnbListing, InsertAirbnbListing,
  SyncStatus, InsertSyncStatus
} from "@shared/schema";

import session from "express-session";
import { eq, and, asc, desc, like, sql, count } from "drizzle-orm";
import { db } from "./db";
import createMemoryStore from "memorystore";

const MemoryStore = createMemoryStore(session);

// Storage interface for the application
export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, updates: Partial<InsertUser>): Promise<User | undefined>;

  // Hotel operations
  getHotel(id: number): Promise<Hotel | undefined>;
  createHotel(hotel: InsertHotel): Promise<Hotel>;
  listHotels(): Promise<Hotel[]>;

  // Platform token operations
  getPlatformToken(hotelId: number, platform: string): Promise<PlatformToken | undefined>;
  savePlatformToken(token: InsertPlatformToken): Promise<PlatformToken>;
  deletePlatformToken(hotelId: number, platform: string): Promise<void>;

  // Review operations
  getReview(id: number): Promise<Review | undefined>;
  getReviewByExternalId(platform: string, externalId: string): Promise<Review | undefined>;
  listReviews(hotelId: number, filters?: {
    platform?: string,
    isReplied?: boolean,
    search?: string,
    sortBy?: string,
    page?: number,
    limit?: number
  }): Promise<{ reviews: Review[], total: number }>;
  saveReview(review: InsertReview): Promise<Review>;

  // Reply operations
  getReply(id: number): Promise<Reply | undefined>;
  getReplyByReviewId(reviewId: number): Promise<Reply | undefined>;
  saveReply(reply: InsertReply): Promise<Reply>;
  markReviewAsReplied(reviewId: number): Promise<void>;

  // Google Business Location operations
  getGoogleBusinessLocation(id: number): Promise<GoogleBusinessLocation | undefined>;
  getGoogleBusinessLocationByGoogleId(googleLocationId: string): Promise<GoogleBusinessLocation | undefined>;
  listGoogleBusinessLocations(hotelId: number): Promise<GoogleBusinessLocation[]>;
  saveGoogleBusinessLocation(location: InsertGoogleBusinessLocation): Promise<GoogleBusinessLocation>;
  updateGoogleBusinessLocation(id: number, updates: Partial<InsertGoogleBusinessLocation>): Promise<GoogleBusinessLocation>;

  // TripAdvisor Location operations
  getTripAdvisorLocation(id: number): Promise<TripAdvisorLocation | undefined>;
  getTripAdvisorLocationByLocationId(tripAdvisorLocationId: string): Promise<TripAdvisorLocation | undefined>;
  listTripAdvisorLocations(hotelId: number): Promise<TripAdvisorLocation[]>;
  saveTripAdvisorLocation(location: InsertTripAdvisorLocation): Promise<TripAdvisorLocation>;
  updateTripAdvisorLocation(id: number, updates: Partial<InsertTripAdvisorLocation>): Promise<TripAdvisorLocation>;

  // Booking.com Property operations
  getBookingProperty(id: number): Promise<BookingProperty | undefined>;
  getBookingPropertyByHotelId(bookingHotelId: string): Promise<BookingProperty | undefined>;
  listBookingProperties(hotelId: number): Promise<BookingProperty[]>;
  saveBookingProperty(property: InsertBookingProperty): Promise<BookingProperty>;
  updateBookingProperty(id: number, updates: Partial<InsertBookingProperty>): Promise<BookingProperty>;

  // Airbnb Listing operations
  getAirbnbListing(id: number): Promise<AirbnbListing | undefined>;
  getAirbnbListingByListingId(airbnbListingId: string): Promise<AirbnbListing | undefined>;
  listAirbnbListings(hotelId: number): Promise<AirbnbListing[]>;
  saveAirbnbListing(listing: InsertAirbnbListing): Promise<AirbnbListing>;
  updateAirbnbListing(id: number, updates: Partial<InsertAirbnbListing>): Promise<AirbnbListing>;

  // Sync Status operations
  getSyncStatus(locationId: number, platform: string): Promise<SyncStatus | undefined>;
  saveSyncStatus(syncStatus: InsertSyncStatus): Promise<SyncStatus>;
  updateSyncStatus(locationId: number, platform: string, updates: Partial<InsertSyncStatus>): Promise<SyncStatus>;
  listSyncStatuses(hotelId?: number): Promise<SyncStatus[]>;

  // Session store for authentication
  sessionStore: any;
}

// In-memory storage implementation
export class DatabaseStorage implements IStorage {
  sessionStore: any;

  constructor() {
    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000 // prune expired entries every 24h
    });

    // Create a default hotel for demonstration
    this.checkAndCreateDefaultHotel();
  }

  private async checkAndCreateDefaultHotel() {
    const existingHotels = await db.select().from(hotels);
    if (existingHotels.length === 0) {
      await this.createHotel({
        name: "Hotel Majestic",
        address: "123 Main Street, Anytown, USA"
      });
    }
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const results = await db.select().from(users).where(eq(users.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const results = await db.select().from(users).where(eq(users.username, username));
    return results.length > 0 ? results[0] : undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const results = await db.insert(users)
      .values({ ...insertUser, role: "staff", hotelId: 1 })
      .returning();
    return results[0];
  }

  async updateUser(id: number, updates: Partial<InsertUser>): Promise<User | undefined> {
    const results = await db.update(users)
      .set(updates)
      .where(eq(users.id, id))
      .returning();
    return results.length > 0 ? results[0] : undefined;
  }

  // Hotel operations
  async getHotel(id: number): Promise<Hotel | undefined> {
    const results = await db.select().from(hotels).where(eq(hotels.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async createHotel(insertHotel: InsertHotel): Promise<Hotel> {
    const results = await db.insert(hotels)
      .values(insertHotel)
      .returning();
    return results[0];
  }

  async listHotels(): Promise<Hotel[]> {
    return db.select().from(hotels);
  }

  // Platform token operations
  async getPlatformToken(hotelId: number, platform: string): Promise<PlatformToken | undefined> {
    const results = await db.select().from(platformTokens)
      .where(
        and(
          eq(platformTokens.hotelId, hotelId),
          eq(platformTokens.platform, platform)
        )
      );
    return results.length > 0 ? results[0] : undefined;
  }

  async savePlatformToken(insertToken: InsertPlatformToken): Promise<PlatformToken> {
    // Check if token already exists for this hotel/platform combination
    const existingToken = await this.getPlatformToken(insertToken.hotelId, insertToken.platform);

    if (existingToken) {
      // Update existing token
      const results = await db.update(platformTokens)
        .set({
          accessToken: insertToken.accessToken,
          refreshToken: insertToken.refreshToken,
          expiresAt: insertToken.expiresAt,
          googleAccountId: insertToken.googleAccountId,
          googleAccountName: insertToken.googleAccountName
        })
        .where(eq(platformTokens.id, existingToken.id))
        .returning();
      return results[0];
    } else {
      // Create new token
      const results = await db.insert(platformTokens)
        .values(insertToken)
        .returning();
      return results[0];
    }
  }

  async deletePlatformToken(hotelId: number, platform: string): Promise<void> {
    await db.delete(platformTokens)
      .where(
        and(
          eq(platformTokens.hotelId, hotelId),
          eq(platformTokens.platform, platform)
        )
      );
  }

  // Review operations
  async getReview(id: number): Promise<Review | undefined> {
    const results = await db.select().from(reviews).where(eq(reviews.id, id));
    if (results.length === 0) return undefined;

    const review = results[0];

    // Check if there's a reply for this review
    const reply = await this.getReplyByReviewId(id);
    if (reply) {
      return { ...review, reply };
    }
    return review;
  }

  async getReviewByExternalId(platform: string, externalId: string): Promise<Review | undefined> {
    const results = await db.select().from(reviews)
      .where(
        and(
          eq(reviews.platform, platform),
          eq(reviews.externalId, externalId)
        )
      );

    if (results.length === 0) return undefined;

    const review = results[0];

    // Check if there's a reply for this review
    const reply = await this.getReplyByReviewId(review.id);
    if (reply) {
      return { ...review, reply };
    }
    return review;
  }

  async listReviews(hotelId: number, filters?: {
    platform?: string,
    isReplied?: boolean,
    search?: string,
    sortBy?: string,
    page?: number,
    limit?: number
  }): Promise<{ reviews: Review[], total: number }> {
    // Build the where conditions
    let whereConditions = [eq(reviews.hotelId, hotelId)];

    if (filters) {
      if (filters.platform && filters.platform !== 'all') {
        whereConditions.push(eq(reviews.platform, filters.platform));
      }

      if (filters.isReplied !== undefined) {
        whereConditions.push(eq(reviews.isReplied, filters.isReplied ? 1 : 0));
      }

      if (filters.search) {
        const searchPattern = `%${filters.search}%`;
        whereConditions.push(
          sql`(${reviews.content} ILIKE ${searchPattern} OR ${reviews.authorName} ILIKE ${searchPattern})`
        );
      }
    }

    // Count total matching reviews
    const countResult = await db.select({ count: count() })
      .from(reviews)
      .where(and(...whereConditions));

    const total = Number(countResult[0].count);

    // Build the query for reviews
    let reviewsList: typeof reviews.$inferSelect[] = [];

    // Determine sort order based on filters
    const sortBy = filters?.sortBy || 'date-desc';

    // Apply correct Drizzle query based on sort and pagination
    try {
      // Use a simpler approach just using the Drizzle query builder with separate cases
      // to work around TypeScript limitations
      if (filters?.page !== undefined && filters?.limit !== undefined) {
        // With pagination
        const offset = (filters.page - 1) * filters.limit;

        if (sortBy === 'date-desc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.date))
            .limit(filters.limit)
            .offset(offset);
        } else if (sortBy === 'date-asc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(asc(reviews.date))
            .limit(filters.limit)
            .offset(offset);
        } else if (sortBy === 'rating-desc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.rating))
            .limit(filters.limit)
            .offset(offset);
        } else if (sortBy === 'rating-asc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(asc(reviews.rating))
            .limit(filters.limit)
            .offset(offset);
        } else {
          // Default sort
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.date))
            .limit(filters.limit)
            .offset(offset);
        }
      } else {
        // Without pagination
        if (sortBy === 'date-desc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.date));
        } else if (sortBy === 'date-asc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(asc(reviews.date));
        } else if (sortBy === 'rating-desc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.rating));
        } else if (sortBy === 'rating-asc') {
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(asc(reviews.rating));
        } else {
          // Default sort
          reviewsList = await db.select().from(reviews)
            .where(and(...whereConditions))
            .orderBy(desc(reviews.date));
        }
      }
    } catch (error) {
      console.error('Error fetching reviews:', error);
      // Return empty list on error
      reviewsList = [];
    }

    // Attach replies to reviews
    const reviewsWithReplies = await Promise.all(
      reviewsList.map(async (review) => {
        const reply = await this.getReplyByReviewId(review.id);
        if (reply) {
          return { ...review, reply };
        }
        return review;
      })
    );

    return { reviews: reviewsWithReplies, total };
  }

  async saveReview(insertReview: InsertReview): Promise<Review> {
    const results = await db.insert(reviews)
      .values({ ...insertReview, isReplied: 0 })
      .returning();
    return results[0];
  }

  // Reply operations
  async getReply(id: number): Promise<Reply | undefined> {
    const results = await db.select().from(replies).where(eq(replies.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async getReplyByReviewId(reviewId: number): Promise<Reply | undefined> {
    const results = await db.select().from(replies).where(eq(replies.reviewId, reviewId));
    return results.length > 0 ? results[0] : undefined;
  }

  async saveReply(insertReply: InsertReply): Promise<Reply> {
    const results = await db.insert(replies)
      .values({
        ...insertReply,
        date: Date.now(),
        isPosted: 0
      })
      .returning();

    // Mark the review as replied
    await this.markReviewAsReplied(insertReply.reviewId);

    return results[0];
  }

  async markReviewAsReplied(reviewId: number): Promise<void> {
    await db.update(reviews)
      .set({ isReplied: 1 })
      .where(eq(reviews.id, reviewId));
  }

  // Google Business Location operations
  async getGoogleBusinessLocation(id: number): Promise<GoogleBusinessLocation | undefined> {
    const results = await db.select().from(googleBusinessLocations).where(eq(googleBusinessLocations.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async getGoogleBusinessLocationByGoogleId(googleLocationId: string): Promise<GoogleBusinessLocation | undefined> {
    const results = await db.select().from(googleBusinessLocations)
      .where(eq(googleBusinessLocations.googleLocationId, googleLocationId));
    return results.length > 0 ? results[0] : undefined;
  }

  async listGoogleBusinessLocations(hotelId: number): Promise<GoogleBusinessLocation[]> {
    return db.select().from(googleBusinessLocations)
      .where(eq(googleBusinessLocations.hotelId, hotelId));
  }

  async saveGoogleBusinessLocation(insertLocation: InsertGoogleBusinessLocation): Promise<GoogleBusinessLocation> {
    // Check if location already exists
    const existing = await this.getGoogleBusinessLocationByGoogleId(insertLocation.googleLocationId);

    if (existing) {
      // Update existing location
      const results = await db.update(googleBusinessLocations)
        .set({
          ...insertLocation,
          updatedAt: Date.now()
        })
        .where(eq(googleBusinessLocations.id, existing.id))
        .returning();
      return results[0];
    } else {
      // Create new location
      const results = await db.insert(googleBusinessLocations)
        .values({
          ...insertLocation,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        })
        .returning();
      return results[0];
    }
  }

  async updateGoogleBusinessLocation(id: number, updates: Partial<InsertGoogleBusinessLocation>): Promise<GoogleBusinessLocation> {
    const results = await db.update(googleBusinessLocations)
      .set({
        ...updates,
        updatedAt: Date.now()
      })
      .where(eq(googleBusinessLocations.id, id))
      .returning();
    return results[0];
  }

  // TripAdvisor Location operations
  async getTripAdvisorLocation(id: number): Promise<TripAdvisorLocation | undefined> {
    const results = await db.select().from(tripAdvisorLocations).where(eq(tripAdvisorLocations.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async getTripAdvisorLocationByLocationId(tripAdvisorLocationId: string): Promise<TripAdvisorLocation | undefined> {
    const results = await db.select().from(tripAdvisorLocations)
      .where(eq(tripAdvisorLocations.tripAdvisorLocationId, tripAdvisorLocationId));
    return results.length > 0 ? results[0] : undefined;
  }

  async listTripAdvisorLocations(hotelId: number): Promise<TripAdvisorLocation[]> {
    return db.select().from(tripAdvisorLocations)
      .where(eq(tripAdvisorLocations.hotelId, hotelId));
  }

  async saveTripAdvisorLocation(insertLocation: InsertTripAdvisorLocation): Promise<TripAdvisorLocation> {
    // Check if location already exists
    const existing = await this.getTripAdvisorLocationByLocationId(insertLocation.tripAdvisorLocationId);

    if (existing) {
      // Update existing location
      const results = await db.update(tripAdvisorLocations)
        .set({
          ...insertLocation,
          updatedAt: Date.now()
        })
        .where(eq(tripAdvisorLocations.id, existing.id))
        .returning();
      return results[0];
    } else {
      // Create new location
      const results = await db.insert(tripAdvisorLocations)
        .values({
          ...insertLocation,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        })
        .returning();
      return results[0];
    }
  }

  async updateTripAdvisorLocation(id: number, updates: Partial<InsertTripAdvisorLocation>): Promise<TripAdvisorLocation> {
    const results = await db.update(tripAdvisorLocations)
      .set({
        ...updates,
        updatedAt: Date.now()
      })
      .where(eq(tripAdvisorLocations.id, id))
      .returning();
    return results[0];
  }

  // Booking.com Property operations
  async getBookingProperty(id: number): Promise<BookingProperty | undefined> {
    const results = await db.select().from(bookingProperties).where(eq(bookingProperties.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async getBookingPropertyByHotelId(bookingHotelId: string): Promise<BookingProperty | undefined> {
    const results = await db.select().from(bookingProperties)
      .where(eq(bookingProperties.bookingHotelId, bookingHotelId));
    return results.length > 0 ? results[0] : undefined;
  }

  async listBookingProperties(hotelId: number): Promise<BookingProperty[]> {
    return db.select().from(bookingProperties)
      .where(eq(bookingProperties.hotelId, hotelId));
  }

  async saveBookingProperty(insertProperty: InsertBookingProperty): Promise<BookingProperty> {
    // Check if property already exists
    const existing = await this.getBookingPropertyByHotelId(insertProperty.bookingHotelId);

    if (existing) {
      // Update existing property
      const results = await db.update(bookingProperties)
        .set({
          ...insertProperty,
          updatedAt: Date.now()
        })
        .where(eq(bookingProperties.id, existing.id))
        .returning();
      return results[0];
    } else {
      // Create new property
      const results = await db.insert(bookingProperties)
        .values({
          ...insertProperty,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        })
        .returning();
      return results[0];
    }
  }

  async updateBookingProperty(id: number, updates: Partial<InsertBookingProperty>): Promise<BookingProperty> {
    const results = await db.update(bookingProperties)
      .set({
        ...updates,
        updatedAt: Date.now()
      })
      .where(eq(bookingProperties.id, id))
      .returning();
    return results[0];
  }

  // Airbnb Listing operations
  async getAirbnbListing(id: number): Promise<AirbnbListing | undefined> {
    const results = await db.select().from(airbnbListings).where(eq(airbnbListings.id, id));
    return results.length > 0 ? results[0] : undefined;
  }

  async getAirbnbListingByListingId(airbnbListingId: string): Promise<AirbnbListing | undefined> {
    const results = await db.select().from(airbnbListings)
      .where(eq(airbnbListings.airbnbListingId, airbnbListingId));
    return results.length > 0 ? results[0] : undefined;
  }

  async listAirbnbListings(hotelId: number): Promise<AirbnbListing[]> {
    return db.select().from(airbnbListings)
      .where(eq(airbnbListings.hotelId, hotelId));
  }

  async saveAirbnbListing(insertListing: InsertAirbnbListing): Promise<AirbnbListing> {
    // Check if listing already exists
    const existing = await this.getAirbnbListingByListingId(insertListing.airbnbListingId);

    if (existing) {
      // Update existing listing
      const results = await db.update(airbnbListings)
        .set({
          ...insertListing,
          updatedAt: Date.now()
        })
        .where(eq(airbnbListings.id, existing.id))
        .returning();
      return results[0];
    } else {
      // Create new listing
      const results = await db.insert(airbnbListings)
        .values({
          ...insertListing,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        })
        .returning();
      return results[0];
    }
  }

  async updateAirbnbListing(id: number, updates: Partial<InsertAirbnbListing>): Promise<AirbnbListing> {
    const results = await db.update(airbnbListings)
      .set({
        ...updates,
        updatedAt: Date.now()
      })
      .where(eq(airbnbListings.id, id))
      .returning();
    return results[0];
  }

  // Sync Status operations
  async getSyncStatus(locationId: number, platform: string): Promise<SyncStatus | undefined> {
    const results = await db.select().from(syncStatus)
      .where(
        and(
          eq(syncStatus.locationId, locationId),
          eq(syncStatus.platform, platform)
        )
      );
    return results.length > 0 ? results[0] : undefined;
  }

  async saveSyncStatus(insertSyncStatus: InsertSyncStatus): Promise<SyncStatus> {
    // Check if sync status already exists
    const existing = await this.getSyncStatus(insertSyncStatus.locationId, insertSyncStatus.platform);

    if (existing) {
      // Update existing sync status
      const results = await db.update(syncStatus)
        .set({
          ...insertSyncStatus,
          updatedAt: Date.now()
        })
        .where(eq(syncStatus.id, existing.id))
        .returning();
      return results[0];
    } else {
      // Create new sync status
      const results = await db.insert(syncStatus)
        .values({
          ...insertSyncStatus,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        })
        .returning();
      return results[0];
    }
  }

  async updateSyncStatus(locationId: number, platform: string, updates: Partial<InsertSyncStatus>): Promise<SyncStatus> {
    const existing = await this.getSyncStatus(locationId, platform);

    if (!existing) {
      throw new Error(`Sync status not found for location ${locationId} and platform ${platform}`);
    }

    const results = await db.update(syncStatus)
      .set({
        ...updates,
        updatedAt: Date.now()
      })
      .where(eq(syncStatus.id, existing.id))
      .returning();
    return results[0];
  }

  async listSyncStatuses(hotelId?: number): Promise<SyncStatus[]> {
    if (hotelId) {
      // Join with google business locations to filter by hotel
      return db.select({
        id: syncStatus.id,
        locationId: syncStatus.locationId,
        platform: syncStatus.platform,
        lastSyncAt: syncStatus.lastSyncAt,
        lastSyncStatus: syncStatus.lastSyncStatus,
        lastSyncError: syncStatus.lastSyncError,
        reviewsCount: syncStatus.reviewsCount,
        newReviewsCount: syncStatus.newReviewsCount,
        nextSyncAt: syncStatus.nextSyncAt,
        syncIntervalMinutes: syncStatus.syncIntervalMinutes,
        isEnabled: syncStatus.isEnabled,
        createdAt: syncStatus.createdAt,
        updatedAt: syncStatus.updatedAt,
      })
      .from(syncStatus)
      .innerJoin(googleBusinessLocations, eq(syncStatus.locationId, googleBusinessLocations.id))
      .where(eq(googleBusinessLocations.hotelId, hotelId));
    } else {
      return db.select().from(syncStatus);
    }
  }
}

export const storage = new DatabaseStorage();

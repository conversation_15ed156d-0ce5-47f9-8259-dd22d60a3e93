import { describe, it, expect, beforeAll, afterAll, beforeEach, jest } from '@jest/globals';
import request from 'supertest';
import { app } from '../index';
import { storage } from '../storage';
import { googleBusinessAPI } from '../services/google-business-api';

describe('Google Business Profile E2E Tests', () => {
  let server: any;
  let testUserId: number;
  let testHotelId: number;

  beforeAll(async () => {
    // Start the server
    server = app.listen(0); // Use random port for testing
    
    // Create test user and hotel
    const testUser = await storage.createUser({
      username: 'test-google-user',
      password: 'test-password',
      fullName: 'Test Google User'
    });
    testUserId = testUser.id;

    const testHotel = await storage.createHotel({
      name: 'Test Hotel for Google',
      address: '123 Test Street, Test City'
    });
    testHotelId = testHotel.id;

    // Update user with hotel ID
    await storage.updateUser(testUserId, { hotelId: testHotelId });
  });

  afterAll(async () => {
    if (server) {
      server.close();
    }
  });

  beforeEach(async () => {
    // Clean up any existing tokens
    try {
      await storage.deletePlatformToken(testHotelId, 'google');
    } catch (error) {
      // Ignore if token doesn't exist
    }
  });

  describe('Google API Configuration', () => {
    it('should return Google API configuration status', async () => {
      const response = await request(app)
        .get('/api/google/test')
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('hasCredentials');
      expect(response.body.hasCredentials).toBe(true);
      expect(response.body).toHaveProperty('authUrl');
      expect(response.body).toHaveProperty('clientId');
      expect(response.body).toHaveProperty('redirectUri');
    });
  });

  describe('OAuth Flow', () => {
    it('should generate Google OAuth URL', async () => {
      // Mock authentication
      const agent = request.agent(app);
      
      const response = await agent
        .get('/api/auth/google')
        .expect(200);

      expect(response.body).toHaveProperty('authUrl');
      expect(response.body.authUrl).toContain('accounts.google.com');
      expect(response.body.authUrl).toContain('oauth2');
      expect(response.body.authUrl).toContain('business.manage');
    });

    it('should handle OAuth callback with valid code', async () => {
      // Mock the Google token exchange
      jest.spyOn(googleBusinessAPI, 'getTokensFromCode').mockResolvedValue({
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
        expiry_date: Date.now() + 3600000
      });

      const agent = request.agent(app);
      
      const response = await agent
        .get('/api/auth/google/callback')
        .query({ code: 'mock-auth-code' })
        .expect(302); // Redirect

      expect(response.headers.location).toContain('/platforms');
      expect(response.headers.location).toContain('connected=google');
    });
  });

  describe('Platform Status', () => {
    it('should return platform connection statuses', async () => {
      const agent = request.agent(app);
      
      const response = await agent
        .get('/api/platforms/status')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      
      const googlePlatform = response.body.find((p: any) => p.id === 'google');
      expect(googlePlatform).toBeDefined();
      expect(googlePlatform).toHaveProperty('connected');
      expect(googlePlatform).toHaveProperty('lastSync');
      expect(googlePlatform).toHaveProperty('reviewCount');
    });
  });

  describe('Google Business Data Operations', () => {
    beforeEach(async () => {
      // Set up mock token for authenticated requests
      await storage.savePlatformToken({
        hotelId: testHotelId,
        platform: 'google',
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        expiresAt: Date.now() + 3600000
      });
    });

    it('should fetch Google Business accounts', async () => {
      // Mock the Google API response
      jest.spyOn(googleBusinessAPI, 'getAccounts').mockResolvedValue([
        {
          name: 'accounts/test-account',
          accountName: 'Test Business Account',
          type: 'PERSONAL'
        }
      ]);

      const agent = request.agent(app);
      
      const response = await agent
        .get('/api/google/accounts')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toHaveProperty('name');
      expect(response.body[0]).toHaveProperty('accountName');
    });

    it('should fetch Google Business locations', async () => {
      // Mock the Google API response
      jest.spyOn(googleBusinessAPI, 'getLocations').mockResolvedValue([
        {
          name: 'locations/test-location',
          locationName: 'Test Hotel Location',
          primaryPhone: '+**********',
          websiteUri: 'https://testhotel.com',
          address: {
            addressLines: ['123 Test Street'],
            locality: 'Test City',
            administrativeArea: 'Test State',
            postalCode: '12345',
            regionCode: 'US'
          }
        }
      ]);

      const agent = request.agent(app);
      
      const response = await agent
        .get('/api/google/locations')
        .query({ accountName: 'accounts/test-account' })
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
      expect(response.body[0]).toHaveProperty('name');
      expect(response.body[0]).toHaveProperty('locationName');
    });

    it('should sync Google Business reviews', async () => {
      // Mock the Google API response
      jest.spyOn(googleBusinessAPI, 'getReviews').mockResolvedValue({
        reviews: [
          {
            name: 'locations/test/reviews/1',
            reviewId: 'test-review-1',
            reviewer: {
              displayName: 'Test Reviewer',
              profilePhotoUrl: 'https://example.com/photo.jpg'
            },
            starRating: 'FIVE',
            comment: 'Great hotel experience!',
            createTime: new Date().toISOString(),
            updateTime: new Date().toISOString()
          }
        ],
        nextPageToken: undefined,
        totalReviewCount: 1
      });

      const agent = request.agent(app);
      
      const response = await agent
        .post('/api/google/sync-reviews')
        .send({ locationName: 'locations/test-location' })
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('syncedCount');
      expect(response.body).toHaveProperty('totalProcessed');
      expect(response.body.syncedCount).toBeGreaterThanOrEqual(0);
    });

    it('should handle review replies', async () => {
      // First, create a test review
      const testReview = await storage.saveReview({
        hotelId: testHotelId,
        platform: 'google',
        externalId: 'test-review-1',
        authorName: 'Test Reviewer',
        rating: 5,
        content: 'Great hotel!',
        date: Date.now()
      });

      // Mock the Google API reply response
      jest.spyOn(googleBusinessAPI, 'replyToReview').mockResolvedValue({
        success: true,
        message: 'Reply posted successfully',
        requiresManualProcessing: false
      });

      const agent = request.agent(app);
      
      const response = await agent
        .post(`/api/google/reply/${testReview.id}`)
        .send({ replyText: 'Thank you for your review!' })
        .expect(200);

      expect(response.body).toHaveProperty('success');
      expect(response.body).toHaveProperty('message');
      expect(response.body.success).toBe(true);
    });
  });

  describe('Platform Disconnection', () => {
    it('should disconnect from Google Business platform', async () => {
      // Set up a token first
      await storage.savePlatformToken({
        hotelId: testHotelId,
        platform: 'google',
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        expiresAt: Date.now() + 3600000
      });

      const agent = request.agent(app);
      
      const response = await agent
        .post('/api/platforms/google/disconnect')
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('Successfully disconnected');

      // Verify token was removed
      const token = await storage.getPlatformToken(testHotelId, 'google');
      expect(token).toBeUndefined();
    });
  });
});

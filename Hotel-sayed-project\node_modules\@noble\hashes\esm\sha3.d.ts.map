{"version": 3, "file": "sha3.d.ts", "sourceRoot": "", "sources": ["../src/sha3.ts"], "names": [], "mappings": "AAaA,OAAO,EAE6B,IAAI,EAGtC,KAAK,KAAK,EAAE,KAAK,OAAO,EAAE,KAAK,OAAO,EAAE,KAAK,KAAK,EACnD,MAAM,YAAY,CAAC;AAoCpB,kFAAkF;AAClF,wBAAgB,OAAO,CAAC,CAAC,EAAE,WAAW,EAAE,MAAM,GAAE,MAAW,GAAG,IAAI,CAyCjE;AAED,8BAA8B;AAC9B,qBAAa,MAAO,SAAQ,IAAI,CAAC,MAAM,CAAE,YAAW,OAAO,CAAC,MAAM,CAAC;IACjE,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC;IAC5B,SAAS,CAAC,GAAG,SAAK;IAClB,SAAS,CAAC,MAAM,SAAK;IACrB,SAAS,CAAC,QAAQ,UAAS;IAC3B,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC;IAC/B,SAAS,CAAC,SAAS,UAAS;IAErB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,MAAM,CAAC;IACzB,SAAS,CAAC,SAAS,UAAS;IAC5B,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC;gBAIvB,QAAQ,EAAE,MAAM,EAChB,MAAM,EAAE,MAAM,EACd,SAAS,EAAE,MAAM,EACjB,SAAS,UAAQ,EACjB,MAAM,GAAE,MAAW;IAiBrB,KAAK,IAAI,MAAM;IAGf,SAAS,CAAC,MAAM,IAAI,IAAI;IAOxB,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;IAazB,SAAS,CAAC,MAAM,IAAI,IAAI;IAUxB,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAehD,OAAO,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAKpC,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU;IAI9B,UAAU,CAAC,GAAG,EAAE,UAAU,GAAG,UAAU;IAOvC,MAAM,IAAI,UAAU;IAGpB,OAAO,IAAI,IAAI;IAIf,UAAU,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,MAAM;CAehC;AAKD,8BAA8B;AAC9B,eAAO,MAAM,QAAQ,EAAE,KAAyD,CAAC;AACjF,yDAAyD;AACzD,eAAO,MAAM,QAAQ,EAAE,KAAyD,CAAC;AACjF,8BAA8B;AAC9B,eAAO,MAAM,QAAQ,EAAE,KAAyD,CAAC;AACjF,8BAA8B;AAC9B,eAAO,MAAM,QAAQ,EAAE,KAAwD,CAAC;AAEhF,gCAAgC;AAChC,eAAO,MAAM,UAAU,EAAE,KAAyD,CAAC;AACnF,yDAAyD;AACzD,eAAO,MAAM,UAAU,EAAE,KAAyD,CAAC;AACnF,gCAAgC;AAChC,eAAO,MAAM,UAAU,EAAE,KAAyD,CAAC;AACnF,gCAAgC;AAChC,eAAO,MAAM,UAAU,EAAE,KAAwD,CAAC;AAElF,MAAM,MAAM,SAAS,GAAG;IAAE,KAAK,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAQ3C,0CAA0C;AAC1C,eAAO,MAAM,QAAQ,EAAE,OAAgE,CAAC;AACxF,0CAA0C;AAC1C,eAAO,MAAM,QAAQ,EAAE,OAAgE,CAAC"}
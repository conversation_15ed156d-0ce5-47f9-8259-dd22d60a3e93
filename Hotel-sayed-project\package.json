{"name": "hotel-review-manager", "version": "1.0.0", "description": "Enterprise-grade hotel review management system with multi-platform integration", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "tsx watch server/index.ts", "dev:client": "vite", "build": "npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "tsc -p server/tsconfig.json", "start": "node dist/server/index.js", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "tsx server/migrate.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx", "format": "prettier --write ."}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@jridgewell/trace-mapping": "^0.3.25", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.59.16", "@types/compression": "^1.8.1", "@types/node-cache": "^4.1.3", "axios": "^1.10.0", "better-sqlite3": "^11.5.0", "body-parser": "^1.20.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "compression": "^1.8.0", "concurrently": "^9.0.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.36.1", "drizzle-zod": "^0.5.1", "embla-carousel-react": "^8.3.0", "express": "^4.21.1", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "framer-motion": "^11.11.17", "google-auth-library": "^9.14.1", "googleapis": "^144.0.0", "helmet": "^8.1.0", "input-otp": "^1.4.1", "isomorphic-dompurify": "^2.25.0", "lucide-react": "^0.460.0", "memorystore": "^1.6.7", "node-cache": "^5.1.2", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^9.2.0", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-icons": "^5.3.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.13.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.0.0", "wouter": "^3.3.5", "zod": "^3.23.8"}, "devDependencies": {"@types/better-sqlite3": "^7.6.11", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.14", "@types/node": "^22.9.0", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/supertest": "^6.0.3", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.28.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "postcss": "^8.4.49", "supertest": "^7.1.1", "tailwindcss": "^3.4.14", "ts-jest": "^29.2.5", "tsx": "^4.19.2", "typescript": "^5.6.3", "vite": "^5.4.10"}}